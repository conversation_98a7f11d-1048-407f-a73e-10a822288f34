/**
 * LiblibAI API代理服务器
 * 解决CORS问题，安全地处理API密钥
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import hmacsha1 from 'hmacsha1';
import randomString from 'string-random';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// LiblibAI配置
const LIBLIB_CONFIG = {
  baseUrl: 'https://api.liblib.art',
  workflowEndpoint: '/v1/workflows/run',
  queryEndpoint: '/v1/tasks',
  accessKey: process.env.VITE_LIBLIB_ACCESS_KEY,
  secretKey: process.env.VITE_LIBLIB_SECRET_KEY,
  modelId: process.env.VITE_LIBLIB_MODEL_ID || 'default-kontext-model'
};



/**
 * 生成API签名（按照正确的Node.js示例实现）
 */
function generateSignature(uri) {
  const timestamp = Date.now(); // 当前时间戳
  const signatureNonce = randomString(16); // 随机字符串，16位

  // 原文 = URL地址 + "&" + 毫秒时间戳 + "&" + 随机字符串
  const str = `${uri}&${timestamp}&${signatureNonce}`;

  console.log('签名原文:', str);
  console.log('SecretKey:', LIBLIB_CONFIG.secretKey);

  // 使用hmacsha1库生成签名
  const hash = hmacsha1(LIBLIB_CONFIG.secretKey, str);

  // 最后一步：encodeBase64URLSafeString(密文)
  // 生成安全字符串
  const signature = hash
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=+$/, "");

  console.log('生成的签名:', signature);

  return {
    signature,
    timestamp: timestamp.toString(),
    signatureNonce
  };
}



// 根路径响应
app.get('/', (req, res) => {
  res.json({
    message: 'LiblibAI代理服务器运行正常',
    version: '1.0.0',
    endpoints: [
      'GET /api/liblib/config - 检查配置',
      'POST /api/liblib/text2img - 文生图',
      'POST /api/liblib/img2img - 图生图',
      'GET /api/liblib/query/:generateUuid - 查询结果'
    ]
  });
});

// 测试签名生成
app.get('/api/liblib/test-signature', (req, res) => {
  try {
    // 使用固定的测试数据
    const testUri = '/api/generate/webui/text2img/ultra';
    const testTimestamp = 1640995200000;
    const testNonce = 'abcd123456789012'; // 16位随机字符串
    const testContent = `${testUri}&${testTimestamp}&${testNonce}`;

    // 使用正确的hmacsha1库
    const hash = hmacsha1(LIBLIB_CONFIG.secretKey, testContent);
    const signature = hash
      .replace(/\+/g, "-")
      .replace(/\//g, "_")
      .replace(/=+$/, "");

    res.json({
      accessKey: LIBLIB_CONFIG.accessKey,
      secretKey: LIBLIB_CONFIG.secretKey,
      accessKeyExists: !!LIBLIB_CONFIG.accessKey,
      secretKeyExists: !!LIBLIB_CONFIG.secretKey,
      uri: testUri,
      timestamp: testTimestamp,
      nonce: testNonce,
      content: testContent,
      rawBase64: hash,
      urlSafeSignature: signature
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// 检查配置
app.get('/api/liblib/config', (req, res) => {
  const isConfigured = !!(LIBLIB_CONFIG.accessKey && LIBLIB_CONFIG.secretKey);
  res.json({
    configured: isConfigured,
    message: isConfigured ? 'LiblibAI配置正常' : 'LiblibAI配置缺失，请检查环境变量'
  });
});

// 文生图API代理
app.post('/api/liblib/text2img', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;

    if (!prompt) {
      return res.status(400).json({ error: '缺少prompt参数' });
    }

    if (!LIBLIB_CONFIG.accessKey || !LIBLIB_CONFIG.secretKey) {
      return res.status(500).json({ error: 'LiblibAI API配置不完整' });
    }

    const uri = LIBLIB_CONFIG.workflowEndpoint;

    // 生成签名
    const { signature, timestamp, signatureNonce } = generateSignature(uri);

    const url = `${LIBLIB_CONFIG.baseUrl}${uri}`;

    // 按照新API格式构建请求体
    const requestData = {
      prompt: prompt.substring(0, 2000),
      model_id: LIBLIB_CONFIG.modelId,
      // 可选参数
      negative_prompt: options.negative_prompt || '',
      width: options.width || 1024,
      height: options.height || 1024,
      steps: options.steps || 20,
      cfg_scale: options.cfg_scale || 7.0,
      seed: options.seed || -1,
      n_iter: options.n_iter || 1,
      sampler: options.sampler || 'DPM++ 2M Karras'
    };

    console.log('代理请求到LiblibAI:', url);
    console.log('请求数据:', requestData);

    const fetch = (await import('node-fetch')).default;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'AccessKey': LIBLIB_CONFIG.accessKey,
        'Signature': signature,
        'Timestamp': timestamp.toString(),
        'SignatureNonce': signatureNonce
      },
      body: JSON.stringify(requestData)
    });

    const result = await response.json();

    if (!response.ok) {
      console.error('LiblibAI API错误:', result);
      return res.status(response.status).json(result);
    }

    console.log('LiblibAI响应:', result);
    res.json(result);

  } catch (error) {
    console.error('代理服务器错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 图生图API代理
app.post('/api/liblib/img2img', async (req, res) => {
  try {
    const { prompt, imageUrl, options = {} } = req.body;

    if (!prompt || !imageUrl) {
      return res.status(400).json({ error: '缺少prompt或imageUrl参数' });
    }

    if (!LIBLIB_CONFIG.accessKey || !LIBLIB_CONFIG.secretKey) {
      return res.status(500).json({ error: 'LiblibAI API配置不完整' });
    }

    const uri = LIBLIB_CONFIG.workflowEndpoint;

    // 生成签名
    const { signature, timestamp, signatureNonce } = generateSignature(uri);

    const url = `${LIBLIB_CONFIG.baseUrl}${uri}`;

    // 按照新API格式构建请求体
    const requestData = {
      image: imageUrl, // 输入图像的Base64编码字符串或URL
      prompt: prompt.substring(0, 2000),
      model_id: LIBLIB_CONFIG.modelId,
      // 可选参数
      negative_prompt: options.negative_prompt || '',
      strength: options.strength || 0.75,
      steps: options.steps || 20,
      cfg_scale: options.cfg_scale || 7.0,
      seed: options.seed || -1,
      n_iter: options.n_iter || 1,
      sampler: options.sampler || 'DPM++ 2M Karras',
      mask: options.mask || null
    };

    console.log('代理图生图请求到LiblibAI:', url);
    console.log('请求数据:', requestData);

    const fetch = (await import('node-fetch')).default;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'AccessKey': LIBLIB_CONFIG.accessKey,
        'Signature': signature,
        'Timestamp': timestamp.toString(),
        'SignatureNonce': signatureNonce
      },
      body: JSON.stringify(requestData)
    });

    const result = await response.json();

    if (!response.ok) {
      console.error('LiblibAI图生图API错误:', result);
      return res.status(response.status).json(result);
    }

    console.log('LiblibAI图生图响应:', result);
    res.json(result);

  } catch (error) {
    console.error('图生图代理服务器错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 查询结果API代理
app.get('/api/liblib/query/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;

    if (!taskId) {
      return res.status(400).json({ error: '缺少taskId参数' });
    }

    if (!LIBLIB_CONFIG.accessKey || !LIBLIB_CONFIG.secretKey) {
      return res.status(500).json({ error: 'LiblibAI API配置不完整' });
    }

    const uri = `${LIBLIB_CONFIG.queryEndpoint}/${taskId}`;

    // 生成签名
    const { signature, timestamp, signatureNonce } = generateSignature(uri);

    const url = `${LIBLIB_CONFIG.baseUrl}${uri}`;

    const fetch = (await import('node-fetch')).default;
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'AccessKey': LIBLIB_CONFIG.accessKey,
        'Signature': signature,
        'Timestamp': timestamp.toString(),
        'SignatureNonce': signatureNonce
      }
    });

    const result = await response.json();

    if (!response.ok) {
      console.error('LiblibAI查询API错误:', result);
      return res.status(response.status).json(result);
    }

    res.json(result);

  } catch (error) {
    console.error('查询代理服务器错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`LiblibAI代理服务器运行在 http://localhost:${PORT}`);
  console.log('API端点:');
  console.log(`  - 配置检查: GET http://localhost:${PORT}/api/liblib/config`);
  console.log(`  - 文生图: POST http://localhost:${PORT}/api/liblib/text2img`);
  console.log(`  - 图生图: POST http://localhost:${PORT}/api/liblib/img2img`);
  console.log(`  - 查询结果: GET http://localhost:${PORT}/api/liblib/query/:generateUuid`);
});

export default app;
