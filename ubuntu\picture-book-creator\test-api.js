/**
 * 简单的LiblibAI API测试脚本
 */

import fetch from 'node-fetch';
import hmacsha1 from 'hmacsha1';
import randomString from 'string-random';
import dotenv from 'dotenv';

dotenv.config();

const LIBLIB_CONFIG = {
  baseUrl: 'https://api.liblib.art',
  workflowEndpoint: '/v1/workflows/run',
  accessKey: process.env.VITE_LIBLIB_ACCESS_KEY,
  secretKey: process.env.VITE_LIBLIB_SECRET_KEY,
};

function generateSignature(uri) {
  const timestamp = Date.now();
  const signatureNonce = randomString(16);
  const str = `${uri}&${timestamp}&${signatureNonce}`;
  
  console.log('签名原文:', str);
  console.log('SecretKey:', LIBLIB_CONFIG.secretKey);
  
  const hash = hmacsha1(LIBLIB_CONFIG.secretKey, str);
  const signature = hash
    .replace(/\+/g, "-")
    .replace(/\//g, "_")
    .replace(/=+$/, "");
  
  console.log('生成的签名:', signature);
  
  return { signature, timestamp, signatureNonce };
}

async function testAPI() {
  try {
    console.log('开始测试LiblibAI API...');
    
    const uri = LIBLIB_CONFIG.workflowEndpoint;
    const url = `${LIBLIB_CONFIG.baseUrl}${uri}`;
    
    const { signature, timestamp, signatureNonce } = generateSignature(uri);
    
    const requestData = {
      prompt: "A simple test image",
      width: 512,
      height: 512,
      steps: 10
    };
    
    console.log('请求URL:', url);
    console.log('请求数据:', requestData);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'AccessKey': LIBLIB_CONFIG.accessKey,
        'Signature': signature,
        'Timestamp': timestamp.toString(),
        'SignatureNonce': signatureNonce
      },
      body: JSON.stringify(requestData)
    });
    
    console.log('响应状态:', response.status, response.statusText);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('响应内容:', responseText);
    
    if (responseText) {
      try {
        const result = JSON.parse(responseText);
        console.log('解析后的JSON:', result);
      } catch (e) {
        console.log('无法解析为JSON，原始内容:', responseText);
      }
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

testAPI();
