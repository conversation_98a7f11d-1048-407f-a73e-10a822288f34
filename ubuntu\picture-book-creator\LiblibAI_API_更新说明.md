# LiblibAI API 更新说明

## 概述

根据最新的LiblibAI API文档，我们已经将LiblibAI集成代码更新为使用新的API规范。**使用新版API地址但保持旧版签名认证方式**，确保最大兼容性。

## 主要变更

### 1. API端点变更
- **旧版**: `https://openapi.liblibai.cloud/api/generate/webui/text2img/ultra`
- **新版**: `https://api.liblib.art/v1/workflows/run`

### 2. 认证方式保持
- **保持**: 复杂的签名认证（AccessKey + SecretKey + HMAC-SHA1签名）
- **请求头**: AccessKey、Signature、Timestamp、SignatureNonce

### 3. 请求格式变更
- **旧版**: 使用`templateUuid`和`generateParams`嵌套结构
- **新版**: 直接在请求体中包含所有参数（基于Kontext模型格式）

### 4. 响应格式变更
- **旧版**: 返回`generateUuid`
- **新版**: 返回`task_id`或`id`

## 配置说明

### 环境变量配置

在`.env`文件中配置以下变量：

```bash
# LiblibAI API配置 (新版API地址 + 签名认证)
VITE_LIBLIB_ACCESS_KEY=your_access_key_here
VITE_LIBLIB_SECRET_KEY=your_secret_key_here
VITE_LIBLIB_MODEL_ID=your_model_id_here
```

### 获取API密钥

1. 访问 [LiblibAI官网](https://www.liblib.art/)
2. 注册并登录账户
3. 在API管理页面获取AccessKey和SecretKey
4. 选择或创建基于Kontext的自定义模型，获取模型ID

## API调用示例

### 文生图 (Text-to-Image)

```javascript
import { generateTextToImageComplete } from './lib/liblibai.js';

const result = await generateTextToImageComplete(
  "A simple, clean, friendly cartoon illustration for a children's picture book.",
  (message, progress) => {
    console.log(`${message} - ${progress}%`);
  },
  {
    width: 1024,
    height: 1024,
    steps: 20,
    cfg_scale: 7.0
  }
);
```

### 图生图 (Image-to-Image)

```javascript
import { generateImageToImageComplete } from './lib/liblibai.js';

const result = await generateImageToImageComplete(
  "A simple, clean, friendly cartoon illustration for a children's picture book.",
  "https://example.com/reference-image.jpg", // 或Base64编码
  (message, progress) => {
    console.log(`${message} - ${progress}%`);
  },
  {
    strength: 0.75,
    steps: 20,
    cfg_scale: 7.0
  }
);
```

## 支持的参数

### 文生图参数

| 参数名 | 类型 | 必须 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `prompt` | string | 是 | - | 图像生成提示词 |
| `model_id` | string | 是 | - | 模型ID |
| `negative_prompt` | string | 否 | "" | 负面提示词 |
| `width` | integer | 否 | 1024 | 图像宽度 |
| `height` | integer | 否 | 1024 | 图像高度 |
| `steps` | integer | 否 | 20 | 采样步数 |
| `cfg_scale` | number | 否 | 7.0 | CFG比例 |
| `seed` | integer | 否 | -1 | 随机种子 |
| `n_iter` | integer | 否 | 1 | 生成数量 |
| `sampler` | string | 否 | "DPM++ 2M Karras" | 采样器 |

### 图生图额外参数

| 参数名 | 类型 | 必须 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `image` | string | 是 | - | 输入图像URL或Base64 |
| `strength` | number | 否 | 0.75 | 变化强度 |
| `mask` | string | 否 | null | 蒙版图像 |

## 状态码说明

查询任务状态时，可能返回以下状态：

- `pending`: 任务排队中
- `running`: 任务执行中
- `processing`: 图像处理中
- `completed`: 任务完成
- `success`: 任务成功
- `failed`: 任务失败
- `error`: 发生错误

## 错误处理

新版API提供更详细的错误信息：

```javascript
try {
  const result = await generateTextToImageComplete(prompt);
} catch (error) {
  console.error('生成失败:', error.message);
  // 错误信息包含具体的失败原因
}
```

## 迁移指南

如果你之前使用旧版API，请按以下步骤迁移：

1. 更新环境变量配置
2. 获取新的API密钥和模型ID
3. 更新代码中的函数调用（API保持兼容）
4. 测试新的API调用

## 注意事项

1. 新版API不再需要代理服务器，直接从前端调用
2. API密钥会在浏览器中暴露，请注意安全性
3. 建议在生产环境中通过后端代理调用API
4. 确保模型ID正确，否则会导致调用失败

## 技术支持

如果在使用过程中遇到问题，请：

1. 检查API密钥和模型ID是否正确
2. 查看浏览器控制台的错误信息
3. 参考LiblibAI官方文档
4. 联系技术支持团队
