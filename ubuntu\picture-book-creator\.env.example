# OpenAI API配置
VITE_OPENAI_API_KEY=your_openai_api_key_here

# LiblibAI API配置 (新版API - 基于Kontext模型)
# 获取API密钥: https://www.liblib.art/
VITE_LIBLIB_API_KEY=your_liblib_api_key_here
VITE_LIBLIB_MODEL_ID=your_kontext_model_id_here

# 旧版LiblibAI配置 (已弃用)
# VITE_LIBLIB_ACCESS_KEY=your_access_key_here
# VITE_LIBLIB_SECRET_KEY=your_secret_key_here

# 使用说明：
# 1. 复制此文件为 .env
# 2. 填入你的实际API密钥
# 3. 重启开发服务器
#
# 注意：新版LiblibAI API使用Bearer Token认证方式，
# 请使用VITE_LIBLIB_API_KEY和VITE_LIBLIB_MODEL_ID配置
