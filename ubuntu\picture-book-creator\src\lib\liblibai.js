/**
 * LiblibAI x 星流图像大模型API集成模块
 * 支持文生图(text2image)和图生图(image2image)功能
 *
 * API文档: https://openapi.liblibai.cloud
 */

// LiblibAI API配置 - 使用本地代理服务器
const LIBLIB_CONFIG = {
  baseUrl: 'http://localhost:3001', // 本地代理服务器
  text2imgEndpoint: '/api/liblib/text2img',
  img2imgEndpoint: '/api/liblib/img2img',
  queryEndpoint: '/api/liblib/query',
  configEndpoint: '/api/liblib/config'
};

/**
 * 构建请求头（简化版，用于代理服务器）
 * @returns {Object} 请求头对象
 */
function buildHeaders() {
  return {
    'Content-Type': 'application/json'
  };
}

/**
 * 文生图API调用
 * @param {string} prompt - 图像生成提示词（英文，不超过2000字符）
 * @param {Object} options - 可选参数
 * @returns {Promise<Object>} 包含task_id的响应对象
 */
export async function generateTextToImage(prompt, options = {}) {
  try {
    console.log('LiblibAI - 开始文生图请求:', prompt);

    const url = LIBLIB_CONFIG.baseUrl + LIBLIB_CONFIG.text2imgEndpoint;
    const headers = buildHeaders();

    const requestData = {
      prompt: prompt.substring(0, 2000), // 确保不超过2000字符
      options: options
    };

    console.log('LiblibAI - 发送请求到代理服务器:', url);
    console.log('LiblibAI - 请求数据:', requestData);

    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(`LiblibAI代理请求失败: ${response.status} ${response.statusText} - ${errorData.error}`);
    }

    const result = await response.json();
    console.log('LiblibAI - 文生图请求成功:', result);

    return result;

  } catch (error) {
    console.error('LiblibAI - 文生图请求失败:', error);
    throw error;
  }
}

/**
 * 图生图API调用
 * @param {string} prompt - 图像生成提示词
 * @param {string} imageUrl - 参考图像URL
 * @param {Object} options - 可选参数
 * @returns {Promise<Object>} 包含task_id的响应对象
 */
export async function generateImageToImage(prompt, imageUrl, options = {}) {
  try {
    console.log('LiblibAI - 开始图生图请求:', { prompt, imageUrl });

    const url = LIBLIB_CONFIG.baseUrl + LIBLIB_CONFIG.img2imgEndpoint;
    const headers = buildHeaders();

    const requestData = {
      prompt: prompt.substring(0, 2000),
      imageUrl: imageUrl,
      options: options
    };

    console.log('LiblibAI - 发送图生图请求到代理服务器:', url);
    console.log('LiblibAI - 请求数据:', requestData);

    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(`LiblibAI图生图代理请求失败: ${response.status} ${response.statusText} - ${errorData.error}`);
    }

    const result = await response.json();
    console.log('LiblibAI - 图生图请求成功:', result);

    return result;

  } catch (error) {
    console.error('LiblibAI - 图生图请求失败:', error);
    throw error;
  }
}

/**
 * 查询生成结果
 * @param {string} taskId - 生成任务的ID
 * @returns {Promise<Object>} 包含生成状态和结果的对象
 */
export async function queryGenerationResult(taskId) {
  try {
    if (!taskId) {
      throw new Error('taskId参数不能为空');
    }

    const url = `${LIBLIB_CONFIG.baseUrl}${LIBLIB_CONFIG.queryEndpoint}/${taskId}`;

    const response = await fetch(url, {
      method: 'GET'
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(`LiblibAI查询代理请求失败: ${response.status} ${response.statusText} - ${errorData.error}`);
    }

    const result = await response.json();
    console.log('LiblibAI - 查询结果:', result);

    return result;

  } catch (error) {
    console.error('LiblibAI - 查询结果失败:', error);
    throw error;
  }
}

/**
 * 等待图像生成完成
 * @param {string} taskId - 生成任务的ID
 * @param {Function} onProgress - 进度回调函数
 * @param {number} maxWaitTime - 最大等待时间（毫秒），默认5分钟
 * @param {number} pollInterval - 轮询间隔（毫秒），默认5秒
 * @returns {Promise<Object>} 生成结果
 */
export async function waitForGeneration(taskId, onProgress = null, maxWaitTime = 300000, pollInterval = 5000) {
  const startTime = Date.now();

  while (Date.now() - startTime < maxWaitTime) {
    try {
      const result = await queryGenerationResult(taskId);

      // 根据LiblibAI API响应格式检查
      if (result.code === 0) {
        // 检查是否有图像数据
        if (result.data?.images && result.data.images.some(image => image !== null)) {
          console.log('LiblibAI - 图像生成成功:', result);
          return result;
        }

        // 任务还在进行中
        if (onProgress) {
          onProgress('processing', result);
        }
        console.log('LiblibAI - 图像生成中，等待完成...');

      } else {
        // API返回错误
        throw new Error(`图像生成失败: ${result.msg || '未知错误'}`);
      }

      // 继续等待
      await new Promise(resolve => setTimeout(resolve, pollInterval));

    } catch (error) {
      console.error('LiblibAI - 查询生成状态时出错:', error);
      throw error;
    }
  }

  throw new Error('图像生成超时，请稍后重试');
}

/**
 * 完整的文生图流程（包含等待和结果获取）
 * @param {string} prompt - 图像生成提示词
 * @param {Function} onProgress - 进度回调函数
 * @param {Object} options - 可选参数
 * @returns {Promise<Object>} 包含图像URL的完整结果
 */
export async function generateTextToImageComplete(prompt, onProgress = null, options = {}) {
  try {
    // 1. 发起文生图请求
    if (onProgress) onProgress('正在发起图像生成请求...', 10);
    const generateResponse = await generateTextToImage(prompt, options);
    // 根据LiblibAI API响应格式提取generateUuid
    const taskId = generateResponse.data?.generateUuid || generateResponse.generateUuid || generateResponse.task_id || generateResponse.id;

    if (!taskId) {
      throw new Error('未获取到生成任务ID');
    }

    // 2. 等待生成完成
    if (onProgress) onProgress('图像生成中，请稍候...', 30);
    const result = await waitForGeneration(
      taskId,
      (status, data) => {
        if (onProgress) {
          const progressMap = {
            'pending': 40,
            'running': 50,
            'processing': 60,
            'completed': 100,
            'success': 100,
            'failed': 0,
            'error': 0
          };
          onProgress(`生成状态: ${status}`, progressMap[status] || 50);
        }
      }
    );

    if (onProgress) onProgress('图像生成完成！', 100);

    // 标准化返回格式，确保兼容性
    return {
      status: 'success',
      imageUrl: result.data?.images?.[0] || result.images?.[0] || result.output?.[0] || result.url || result.image_url,
      taskId: result.data?.generateUuid || taskId,
      originalResponse: result
    };

  } catch (error) {
    console.error('LiblibAI - 完整文生图流程失败:', error);
    if (onProgress) onProgress(`生成失败: ${error.message}`, 0);
    throw error;
  }
}

/**
 * 完整的图生图流程（包含等待和结果获取）
 * @param {string} prompt - 图像生成提示词
 * @param {string} referenceImageUrl - 参考图像URL
 * @param {Function} onProgress - 进度回调函数
 * @param {Object} options - 可选参数
 * @returns {Promise<Object>} 包含图像URL的完整结果
 */
export async function generateImageToImageComplete(prompt, referenceImageUrl, onProgress = null, options = {}) {
  try {
    // 1. 发起图生图请求
    if (onProgress) onProgress('正在发起图生图请求...', 10);
    const generateResponse = await generateImageToImage(prompt, referenceImageUrl, options);
    // 根据LiblibAI API响应格式提取generateUuid
    const taskId = generateResponse.data?.generateUuid || generateResponse.generateUuid || generateResponse.task_id || generateResponse.id;

    if (!taskId) {
      throw new Error('未获取到生成任务ID');
    }

    // 2. 等待生成完成
    if (onProgress) onProgress('图像生成中，请稍候...', 30);
    const result = await waitForGeneration(
      taskId,
      (status, data) => {
        if (onProgress) {
          const progressMap = {
            'pending': 40,
            'running': 50,
            'processing': 60,
            'completed': 100,
            'success': 100,
            'failed': 0,
            'error': 0
          };
          onProgress(`生成状态: ${status}`, progressMap[status] || 50);
        }
      }
    );

    if (onProgress) onProgress('图生图完成！', 100);

    // 标准化返回格式，确保兼容性
    return {
      status: 'success',
      imageUrl: result.data?.images?.[0] || result.images?.[0] || result.output?.[0] || result.url || result.image_url,
      taskId: result.data?.generateUuid || taskId,
      originalResponse: result
    };

  } catch (error) {
    console.error('LiblibAI - 完整图生图流程失败:', error);
    if (onProgress) onProgress(`生成失败: ${error.message}`, 0);
    throw error;
  }
}

/**
 * 检查API配置是否正确（通过代理服务器）
 * @returns {Promise<boolean>} 配置是否完整
 */
export async function checkLiblibConfig() {
  try {
    const url = LIBLIB_CONFIG.baseUrl + LIBLIB_CONFIG.configEndpoint;
    const response = await fetch(url);

    if (!response.ok) {
      return false;
    }

    const result = await response.json();
    return result.configured;

  } catch (error) {
    console.error('检查LiblibAI配置失败:', error);
    return false;
  }
}
