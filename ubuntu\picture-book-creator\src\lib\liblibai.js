/**
 * LiblibAI x 星流图像大模型API集成模块
 * 支持文生图(text2image)和图生图(image2image)功能
 *
 * API文档: https://openapi.liblibai.cloud
 */

// LiblibAI API配置 - 使用新版API地址但保持签名认证
const LIBLIB_CONFIG = {
  baseUrl: 'https://api.liblib.art',
  workflowEndpoint: '/v1/workflows/run',
  queryEndpoint: '/v1/tasks',
  // 从环境变量获取AccessKey和SecretKey
  accessKey: import.meta.env.VITE_LIBLIB_ACCESS_KEY,
  secretKey: import.meta.env.VITE_LIBLIB_SECRET_KEY,
  modelId: import.meta.env.VITE_LIBLIB_MODEL_ID || 'default-kontext-model'
};

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @returns {string} 随机字符串
 */
function generateRandomString(length = 16) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * HMAC-SHA1加密
 * @param {string} key - 密钥
 * @param {string} message - 消息
 * @returns {Promise<ArrayBuffer>} 加密结果
 */
async function hmacSha1(key, message) {
  const encoder = new TextEncoder();
  const keyData = encoder.encode(key);
  const messageData = encoder.encode(message);

  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HMAC', hash: 'SHA-1' },
    false,
    ['sign']
  );

  return await crypto.subtle.sign('HMAC', cryptoKey, messageData);
}

/**
 * 将ArrayBuffer转换为Base64 URL安全字符串
 * @param {ArrayBuffer} buffer - 缓冲区
 * @returns {string} Base64 URL安全字符串
 */
function arrayBufferToBase64UrlSafe(buffer) {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  const base64 = btoa(binary);
  // 转换为URL安全的Base64格式
  return base64
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
}

/**
 * 生成API签名
 * @param {string} uri - API端点URI
 * @returns {Promise<Object>} 包含签名、时间戳和随机字符串的对象
 */
async function generateSignature(uri) {
  if (!LIBLIB_CONFIG.secretKey) {
    throw new Error('LiblibAI SecretKey未配置，请设置VITE_LIBLIB_SECRET_KEY环境变量');
  }

  const timestamp = Date.now();
  const signatureNonce = generateRandomString(16);

  // 构建原文：URL地址 + "&" + 毫秒时间戳 + "&" + 随机字符串
  const content = `${uri}&${timestamp}&${signatureNonce}`;

  // 使用HMAC-SHA1加密
  const hashBuffer = await hmacSha1(LIBLIB_CONFIG.secretKey, content);

  // 转换为Base64 URL安全字符串
  const signature = arrayBufferToBase64UrlSafe(hashBuffer);

  return {
    signature,
    timestamp,
    signatureNonce
  };
}

/**
 * 构建请求头
 * @param {string} uri - API端点URI
 * @returns {Promise<Object>} 请求头对象
 */
async function buildHeaders(uri) {
  if (!LIBLIB_CONFIG.accessKey || !LIBLIB_CONFIG.secretKey) {
    throw new Error('LiblibAI API配置不完整，请设置VITE_LIBLIB_ACCESS_KEY和VITE_LIBLIB_SECRET_KEY环境变量');
  }

  const { signature, timestamp, signatureNonce } = await generateSignature(uri);

  return {
    'Content-Type': 'application/json',
    'AccessKey': LIBLIB_CONFIG.accessKey,
    'Signature': signature,
    'Timestamp': timestamp.toString(),
    'SignatureNonce': signatureNonce
  };
}

/**
 * 文生图API调用
 * @param {string} prompt - 图像生成提示词（英文，不超过2000字符）
 * @param {Object} options - 可选参数
 * @returns {Promise<Object>} 包含task_id的响应对象
 */
export async function generateTextToImage(prompt, options = {}) {
  try {
    console.log('LiblibAI - 开始文生图请求:', prompt);

    const uri = LIBLIB_CONFIG.workflowEndpoint;
    const url = LIBLIB_CONFIG.baseUrl + uri;
    const headers = await buildHeaders(uri);

    // 按照新版API文档格式构建请求体
    const requestData = {
      prompt: prompt.substring(0, 2000), // 确保不超过2000字符
      model_id: LIBLIB_CONFIG.modelId,
      // 可选参数
      negative_prompt: options.negative_prompt || '',
      width: options.width || 1024,
      height: options.height || 1024,
      steps: options.steps || 20,
      cfg_scale: options.cfg_scale || 7.0,
      seed: options.seed || -1,
      n_iter: options.n_iter || 1,
      sampler: options.sampler || 'DPM++ 2M Karras'
    };

    console.log('LiblibAI - 发送请求到官方API:', url);
    console.log('LiblibAI - 请求头:', headers);
    console.log('LiblibAI - 请求数据:', requestData);

    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(`LiblibAI API请求失败: ${response.status} ${response.statusText} - ${errorData.error || errorData.message}`);
    }

    const result = await response.json();
    console.log('LiblibAI - 文生图响应:', result);

    return result;

  } catch (error) {
    console.error('LiblibAI - 文生图请求失败:', error);
    throw error;
  }
}

/**
 * 图生图API调用
 * @param {string} prompt - 图像生成提示词
 * @param {string} imageUrl - 参考图像URL或Base64编码字符串
 * @param {Object} options - 可选参数
 * @returns {Promise<Object>} 包含task_id的响应对象
 */
export async function generateImageToImage(prompt, imageUrl, options = {}) {
  try {
    console.log('LiblibAI - 开始图生图请求:', { prompt, imageUrl });

    const uri = LIBLIB_CONFIG.workflowEndpoint;
    const url = LIBLIB_CONFIG.baseUrl + uri;
    const headers = await buildHeaders(uri);

    // 按照新API文档格式构建请求体
    const requestData = {
      image: imageUrl, // 输入图像的Base64编码字符串或URL
      prompt: prompt.substring(0, 2000),
      model_id: LIBLIB_CONFIG.modelId,
      // 可选参数
      negative_prompt: options.negative_prompt || '',
      strength: options.strength || 0.75, // 控制生成图像与原始图像的相似度
      steps: options.steps || 20,
      cfg_scale: options.cfg_scale || 7.0,
      seed: options.seed || -1,
      n_iter: options.n_iter || 1,
      sampler: options.sampler || 'DPM++ 2M Karras',
      mask: options.mask || null // 蒙版图像，用于局部重绘
    };

    console.log('LiblibAI - 发送图生图请求到官方API:', url);
    console.log('LiblibAI - 请求头:', headers);
    console.log('LiblibAI - 请求数据:', requestData);

    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(`LiblibAI图生图API请求失败: ${response.status} ${response.statusText} - ${errorData.error || errorData.message}`);
    }

    const result = await response.json();
    console.log('LiblibAI - 图生图响应:', result);

    return result;

  } catch (error) {
    console.error('LiblibAI - 图生图请求失败:', error);
    throw error;
  }
}

/**
 * 查询生成结果
 * @param {string} taskId - 生成任务的ID
 * @returns {Promise<Object>} 包含生成状态和结果的对象
 */
export async function queryGenerationResult(taskId) {
  try {
    if (!taskId) {
      throw new Error('taskId参数不能为空');
    }

    const uri = `${LIBLIB_CONFIG.queryEndpoint}/${taskId}`;
    const url = `${LIBLIB_CONFIG.baseUrl}${uri}`;
    const headers = await buildHeaders(uri);

    const response = await fetch(url, {
      method: 'GET',
      headers: headers
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(`LiblibAI查询API请求失败: ${response.status} ${response.statusText} - ${errorData.error || errorData.message}`);
    }

    const result = await response.json();
    console.log('LiblibAI - 查询结果:', result);

    return result;

  } catch (error) {
    console.error('LiblibAI - 查询结果失败:', error);
    throw error;
  }
}

/**
 * 等待图像生成完成
 * @param {string} taskId - 生成任务的ID
 * @param {Function} onProgress - 进度回调函数
 * @param {number} maxWaitTime - 最大等待时间（毫秒），默认5分钟
 * @param {number} pollInterval - 轮询间隔（毫秒），默认5秒
 * @returns {Promise<Object>} 生成结果
 */
export async function waitForGeneration(taskId, onProgress = null, maxWaitTime = 300000, pollInterval = 5000) {
  const startTime = Date.now();

  while (Date.now() - startTime < maxWaitTime) {
    try {
      const result = await queryGenerationResult(taskId);
      const status = result.status;

      if (onProgress) {
        onProgress(status, result);
      }

      if (status === 'completed' || status === 'success') {
        console.log('LiblibAI - 图像生成成功:', result);
        return result;
      } else if (status === 'failed' || status === 'error') {
        throw new Error(`图像生成失败: ${result.message || result.error || '未知错误'}`);
      }

      // 继续等待
      console.log(`LiblibAI - 图像生成中，状态: ${status}`);
      await new Promise(resolve => setTimeout(resolve, pollInterval));

    } catch (error) {
      console.error('LiblibAI - 查询生成状态时出错:', error);
      throw error;
    }
  }

  throw new Error('图像生成超时，请稍后重试');
}

/**
 * 完整的文生图流程（包含等待和结果获取）
 * @param {string} prompt - 图像生成提示词
 * @param {Function} onProgress - 进度回调函数
 * @param {Object} options - 可选参数
 * @returns {Promise<Object>} 包含图像URL的完整结果
 */
export async function generateTextToImageComplete(prompt, onProgress = null, options = {}) {
  try {
    // 1. 发起文生图请求
    if (onProgress) onProgress('正在发起图像生成请求...', 10);
    const generateResponse = await generateTextToImage(prompt, options);
    const taskId = generateResponse.task_id || generateResponse.id;

    if (!taskId) {
      throw new Error('未获取到生成任务ID');
    }

    // 2. 等待生成完成
    if (onProgress) onProgress('图像生成中，请稍候...', 30);
    const result = await waitForGeneration(
      taskId,
      (status, data) => {
        if (onProgress) {
          const progressMap = {
            'pending': 40,
            'running': 50,
            'processing': 60,
            'completed': 100,
            'success': 100,
            'failed': 0,
            'error': 0
          };
          onProgress(`生成状态: ${status}`, progressMap[status] || 50);
        }
      }
    );

    if (onProgress) onProgress('图像生成完成！', 100);

    // 标准化返回格式，确保兼容性
    return {
      status: 'success',
      imageUrl: result.output?.[0] || result.images?.[0] || result.url || result.image_url,
      taskId: result.task_id || result.id,
      originalResponse: result
    };

  } catch (error) {
    console.error('LiblibAI - 完整文生图流程失败:', error);
    if (onProgress) onProgress(`生成失败: ${error.message}`, 0);
    throw error;
  }
}

/**
 * 完整的图生图流程（包含等待和结果获取）
 * @param {string} prompt - 图像生成提示词
 * @param {string} referenceImageUrl - 参考图像URL
 * @param {Function} onProgress - 进度回调函数
 * @param {Object} options - 可选参数
 * @returns {Promise<Object>} 包含图像URL的完整结果
 */
export async function generateImageToImageComplete(prompt, referenceImageUrl, onProgress = null, options = {}) {
  try {
    // 1. 发起图生图请求
    if (onProgress) onProgress('正在发起图生图请求...', 10);
    const generateResponse = await generateImageToImage(prompt, referenceImageUrl, options);
    const taskId = generateResponse.task_id || generateResponse.id;

    if (!taskId) {
      throw new Error('未获取到生成任务ID');
    }

    // 2. 等待生成完成
    if (onProgress) onProgress('图像生成中，请稍候...', 30);
    const result = await waitForGeneration(
      taskId,
      (status, data) => {
        if (onProgress) {
          const progressMap = {
            'pending': 40,
            'running': 50,
            'processing': 60,
            'completed': 100,
            'success': 100,
            'failed': 0,
            'error': 0
          };
          onProgress(`生成状态: ${status}`, progressMap[status] || 50);
        }
      }
    );

    if (onProgress) onProgress('图生图完成！', 100);

    // 标准化返回格式，确保兼容性
    return {
      status: 'success',
      imageUrl: result.output?.[0] || result.images?.[0] || result.url || result.image_url,
      taskId: result.task_id || result.id,
      originalResponse: result
    };

  } catch (error) {
    console.error('LiblibAI - 完整图生图流程失败:', error);
    if (onProgress) onProgress(`生成失败: ${error.message}`, 0);
    throw error;
  }
}

/**
 * 检查API配置是否正确
 * @returns {Promise<boolean>} 配置是否完整
 */
export async function checkLiblibConfig() {
  try {
    // 检查必要的配置项
    if (!LIBLIB_CONFIG.accessKey) {
      console.warn('LiblibAI AccessKey未配置');
      return false;
    }

    if (!LIBLIB_CONFIG.secretKey) {
      console.warn('LiblibAI SecretKey未配置');
      return false;
    }

    if (!LIBLIB_CONFIG.modelId) {
      console.warn('LiblibAI 模型ID未配置');
      return false;
    }

    console.log('LiblibAI配置检查通过');
    return true;

  } catch (error) {
    console.error('检查LiblibAI配置失败:', error);
    return false;
  }
}
